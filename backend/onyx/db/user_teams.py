from collections.abc import Sequence
from operator import and_
from uuid import UUID

from fastapi import HTTP<PERSON>xception
from sqlalchemy import delete
from sqlalchemy import func
from sqlalchemy import Select
from sqlalchemy import select
from sqlalchemy import update
from sqlalchemy.orm import Session

from onyx.server.user_teams.models import CreateUserTeams
from onyx.server.user_teams.models import UpdateUserTeams
from onyx.db.models import DocumentSet__UserGroup
from onyx.db.models import LLMProvider__UserGroup
from onyx.db.models import Persona__UserGroup
from onyx.db.models import TokenRateLimit__UserGroup
from onyx.db.models import Tool__UserTeams
from onyx.db.models import Credential__UserGroup
from onyx.db.models import User
from onyx.db.models import User__UserGroup
from onyx.db.models import UserGroup
from onyx.db.models import UserGroup__ConnectorCredentialPair
from onyx.utils.logger import setup_logger
from onyx.auth.schemas import User<PERSON>ole
from onyx.db.models import InvitedUser
from onyx.auth.status import UserStatus, compute_user_status

logger = setup_logger()


def _validate_user_status_for_team_assignment(user: User) -> None:
    """
    Validate that a user can be assigned to a team based on their current status.

    Args:
        user: The user to validate

    Raises:
        ValueError: If the user cannot be assigned to a team with a descriptive error message
    """
    if user.role == UserRole.ADMIN:
        raise ValueError("Admin users cannot be assigned to teams")

    user_status = compute_user_status(user)
    if user_status != UserStatus.PENDING_ASSIGNMENT:
        if user_status == UserStatus.ACTIVE:
            raise ValueError(f"User '{user.email}' is already active and assigned to a team. Active users cannot be reassigned to different teams.")
        elif user_status == UserStatus.READY_TO_SIGNUP:
            raise ValueError(f"User '{user.email}' is ready to signup and already assigned to a team. Users in signup process cannot be reassigned.")
        elif user_status == UserStatus.INACTIVE:
            raise ValueError(f"User '{user.email}' is inactive and cannot be assigned to teams. Please activate the user first.")
        else:
            raise ValueError(f"User '{user.email}' has status '{user_status.value}' and cannot be assigned to teams. Only users with 'pending_assignment' status can be assigned to teams.")


def create_user_teams(db_session: Session, user_teams: CreateUserTeams) -> UserGroup:
    # Check if a user_team with the same name already exists
    existing_user_teams = db_session.scalar(
        select(UserGroup).where(func.lower(UserGroup.name) == user_teams.name.lower())
    )
    if existing_user_teams is not None:
        raise ValueError("Cannot create user team with this name.")

    # Validate all user_ids
    if user_teams.user_ids:
        valid_user_ids = set(
            user_id for (user_id,) in db_session.execute(
                select(User.id).where(User.id.in_(user_teams.user_ids))
            ).all()
        )
        for user_id in user_teams.user_ids:
            if user_id not in valid_user_ids:
                raise ValueError(f"User_id {user_id} is not valid")
        # Validate user status for team assignment
        users = db_session.execute(
            select(User).where(User.id.in_(user_teams.user_ids))
        ).unique().scalars().all()

        for user in users:
            _validate_user_status_for_team_assignment(user)
    else:
        raise ValueError("User team must have at least one user.")

    db_user_team = UserGroup(
        name=user_teams.name, is_up_to_date=True, time_last_modified_by_user=func.now()
    )
    db_session.add(db_user_team)
    db_session.flush()  # give the user_team an ID

    relationships = []
    for user_id in user_teams.user_ids:
        user = db_session.query(User).filter(User.id == user_id).first()
        if user:
            relationships.append(User__UserGroup(user_id=user_id, user_group_id=db_user_team.id))
        else:
            # Try to find a pending invited user with this email
            invited_users = db_session.query(InvitedUser).filter_by(status="pending_assignment").all()
            invite = next((i for i in invited_users if i.id == user_id), None)
            if invite:
                relationships.append(User__UserGroup(user_id=invite.id, user_group_id=db_user_team.id))
    db_session.add_all(relationships)

    db_session.commit()

    # --- Ensure new team has a default LLM provider ---
    from onyx.db.llm import ensure_all_teams_have_default_llm_provider
    ensure_all_teams_have_default_llm_provider(db_session)
    # --- End LLM provider assignment ---

    # --- Update invited user status in DB if needed ---
    updated = False
    for user_id in user_teams.user_ids:
        db_invite = db_session.query(InvitedUser).filter_by(id=user_id, status="pending_assignment").first()
        if db_invite:
            db_invite.status = "ready_to_signup"
            updated = True
    if updated:
        db_session.commit()
    # --- End update ---

    return db_user_team


def update_user_teams(
    db_session: Session,
    user: User | None,
    user_team_id: int,
    user_team_update: UpdateUserTeams,
) -> UserGroup:
    stmt = select(UserGroup).where(UserGroup.id == user_team_id)
    db_user_team = db_session.scalar(stmt)
    if db_user_team is None:
        raise ValueError(f"User team with id '{user_team_id}' not found")

    if not db_user_team.is_up_to_date:
        raise ValueError("User team does not exist!")

    # Update user_team name if different
    if user_team_update.name != db_user_team.name:
        # Check if another user_team with the same name already exists
        existing_user_team = db_session.scalar(
            select(UserGroup).where(func.lower(UserGroup.name) == user_team_update.name.lower(), UserGroup.id != user_team_id)
        )
        if existing_user_team is not None:
            raise ValueError("Cannot update user team with this name.")
        db_user_team.name = user_team_update.name

    # Validate all user_ids in update
    if user_team_update.user_ids:
        valid_user_ids = set(
            user_id for (user_id,) in db_session.execute(
                select(User.id).where(User.id.in_(user_team_update.user_ids))
            ).all()
        )
        for user_id in user_team_update.user_ids:
            if user_id not in valid_user_ids:
                raise ValueError(f"User_id {user_id} is not valid")

    current_user_ids = set([user.id for user in db_user_team.users])
    updated_user_ids = set(user_team_update.user_ids)
    added_user_ids = list(updated_user_ids - current_user_ids)
    removed_user_ids = list(current_user_ids - updated_user_ids)

    # if (removed_user_ids or added_user_ids) and (
    #     not user or user.role != UserRole.ADMIN
    # ):
    #     raise ValueError("Only admins can add or remove users from user user_team")

    if removed_user_ids:
        where_clause = User__UserGroup.user_id.in_(removed_user_ids)
        user__user_group_relationships = db_session.scalars(
            select(User__UserGroup).where(where_clause)
        ).all()
        for user__user_group_relationship in user__user_group_relationships:
            db_session.delete(user__user_group_relationship)
        db_session.commit()

    if added_user_ids:
        # Validate status of newly added users
        added_users = db_session.execute(
            select(User).where(User.id.in_(added_user_ids))
        ).unique().scalars().all()

        for user in added_users:
            _validate_user_status_for_team_assignment(user)

        relationships = []
        for user_id in added_user_ids:
            user = db_session.query(User).filter(User.id == user_id).first()
            if user:
                relationships.append(User__UserGroup(user_id=user_id, user_group_id=user_team_id))
            else:
                invited_users = db_session.query(InvitedUser).filter_by(status="pending_assignment").all()
                invite = next((i for i in invited_users if i.id == user_id), None)
                if invite:
                    relationships.append(User__UserGroup(user_id=invite.id, user_group_id=user_team_id))
        db_session.add_all(relationships)
        db_session.commit()

    # Mark user_team as modified
    db_user_team.time_last_modified_by_user = func.now()

    db_session.commit()

    # --- Update invited user status in DB if needed ---
    updated = False
    for user_id in added_user_ids:
        db_invite = db_session.query(InvitedUser).filter_by(id=user_id, status="pending_assignment").first()
        if db_invite:
            db_invite.status = "ready_to_signup"
            updated = True
    if updated:
        db_session.commit()
    # --- End update ---

    return db_user_team

def add_user_to_team(
    db_session: Session,
    user_id: int,
    team_id: int,
    invite: InvitedUser | None = None
):
    # Check if already added
    exists = db_session.scalar(
        select(User__UserGroup)
        .where(User__UserGroup.user_id == user_id,
               User__UserGroup.user_group_id == team_id)
    )
    if not exists:
        db_session.add(User__UserGroup(user_id=user_id, user_group_id=team_id))

    if invite and invite.status == "pending_assignment":
        invite.status = "ready_to_signup"
    logger.info(f"add_user_to_team: user_id={user_id}, team_id={team_id}, ")


def get_user_teams(
    db_session: Session, only_up_to_date: bool = True
) -> Sequence[UserGroup]:
    """
    Gets user teams from the database.
    """
    stmt = select(UserGroup)
    if only_up_to_date:
        stmt = stmt.where(UserGroup.is_up_to_date == True)  # noqa: E712
    return db_session.scalars(stmt).all()
def fetch_user_teams_for_user(db_session: Session, user_id):
    """
    Returns a list of User__UserGroup relationships for the given user_id.
    This function is safe to import from anywhere and does not depend on server models.
    """
    return db_session.query(User__UserGroup).filter(User__UserGroup.user_id == user_id).all()

def get_user_teams_for_user(
    db_session: Session, user_id: UUID, only_up_to_date: bool = True
) -> Sequence[UserGroup]:
    """
    Gets user teams that a specific user belongs to.
    """
    stmt = (
        select(UserGroup)
        .join(User__UserGroup, User__UserGroup.user_group_id == UserGroup.id)
        .join(User, User.id == User__UserGroup.user_id)  # type: ignore
        .where(User.id == user_id)  # type: ignore
    )
    if only_up_to_date:
        stmt = stmt.where(UserGroup.is_up_to_date == True)  # noqa: E712
    return db_session.scalars(stmt).all()



def prepare_user_teams_for_deletion(db_session: Session, user_team_id: int) -> None:
    stmt = select(UserGroup).where(UserGroup.id == user_team_id)
    db_user_team = db_session.scalar(stmt)
    if db_user_team is None:
        raise ValueError(f"User team with id '{user_team_id}' not found")

    if not db_user_team.is_up_for_deletion:
        # Collect all associated entities
        associated_entities = []

        # Connectors
        connector_count = db_session.query(UserGroup__ConnectorCredentialPair).filter(
            UserGroup__ConnectorCredentialPair.user_group_id == user_team_id,
            UserGroup__ConnectorCredentialPair.is_current == True
        ).count()
        if connector_count > 0:
            associated_entities.append(f"{connector_count} connectors")

        # Document Sets
        document_set_count = db_session.query(DocumentSet__UserGroup).filter(
            DocumentSet__UserGroup.user_group_id == user_team_id
        ).count()
        if document_set_count > 0:
            associated_entities.append(f"{document_set_count} document sets")

        # Assistants (Personas)
        persona_count = db_session.query(Persona__UserGroup).filter(
            Persona__UserGroup.user_group_id == user_team_id
        ).count()
        if persona_count > 0:
            associated_entities.append(f"{persona_count} assistants")

        # Tools
        tool_count = db_session.query(Tool__UserTeams).filter(
            Tool__UserTeams.user_group_id == user_team_id
        ).count()
        if tool_count > 0:
            associated_entities.append(f"{tool_count} tools")

        # LLMs
        llm_count = db_session.query(LLMProvider__UserGroup).filter(
            LLMProvider__UserGroup.user_group_id == user_team_id
        ).count()
        if llm_count > 0:
            associated_entities.append(f"{llm_count} LLM providers")

        # Credentials
        credential_count = db_session.query(Credential__UserGroup).filter(
            Credential__UserGroup.user_group_id == user_team_id
        ).count()
        if credential_count > 0:
            associated_entities.append(f"{credential_count} credentials")

        if associated_entities:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete team. Please remove the entities assigned to the team first."
            )

        # If all checks pass, proceed with marking for deletion and cleanup
        # cleanup_user__user_group_relationships
        where_clause = User__UserGroup.user_group_id == user_team_id
        user__user_group_relationships = db_session.scalars(
            select(User__UserGroup).where(where_clause)
        ).all()
        for user__user_group_relationship in user__user_group_relationships:
            db_session.delete(user__user_group_relationship)

        # cleanup_document_set__user_group_relationships
        db_session.execute(
            delete(DocumentSet__UserGroup).where(
                DocumentSet__UserGroup.user_group_id == user_team_id
            )
        )

        # cleanup_persona__user_group_relationships
        db_session.query(Persona__UserGroup).filter(
            Persona__UserGroup.user_group_id == user_team_id
        ).delete(synchronize_session=False)

        # cleanup_user_group__cc_pair_relationships
        stmt = select(UserGroup__ConnectorCredentialPair).where(
            UserGroup__ConnectorCredentialPair.user_group_id == user_team_id
        )
        stmt = stmt.where(
            UserGroup__ConnectorCredentialPair.is_current == False  # noqa: E712
        )
        user_group__cc_pair_relationships = db_session.scalars(stmt)
        for user_group__cc_pair_relationship in user_group__cc_pair_relationships:
            db_session.delete(user_group__cc_pair_relationship)

        # cleanup_llm_provider__user_group_relationships
        db_session.query(LLMProvider__UserGroup).filter(
            LLMProvider__UserGroup.user_group_id == user_team_id
        ).delete(synchronize_session=False)

        # cleanup_credential__user_group_relationships
        db_session.query(Credential__UserGroup).filter(
            Credential__UserGroup.user_group_id == user_team_id
        ).delete(synchronize_session=False)

        db_user_team.is_up_to_date = False
        db_user_team.is_up_for_deletion = True
        db_session.commit()
    else:
        raise ValueError(f"User team with id '{user_team_id}' not found or already marked for deletion.")
