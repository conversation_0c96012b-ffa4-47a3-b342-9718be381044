from typing import Any
from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy import func
from sqlalchemy.orm import Session

from onyx.db.models import Tool
from onyx.db.models import User
from onyx.server.features.tool.models import Header
from onyx.utils.headers import Head<PERSON><PERSON>temDict
from onyx.utils.logger import setup_logger
from onyx.db.models import Tool__UserTeams
from onyx.auth.schemas import UserRole

logger = setup_logger()


def get_tools(db_session: Session) -> list[Tool]:
    return list(db_session.scalars(select(Tool)).all())


def get_tools_for_user(db_session: Session, user: User | None) -> list[Tool]:
    """
    Get tools available to a user based on their role and team membership.
    - Admin users see all tools
    - Team admin/basic users see built-in tools + tools assigned to their teams
    """
    if not user:
        return []

    if user.role == UserRole.ADMIN:
        return get_tools(db_session)

    if user.role in (UserRole.TEAM_ADMIN, UserRole.BASIC):
        # Get built-in tools (tools with in_code_tool_id set) - these are available to all users
        built_in_tools = db_session.scalars(
            select(Tool).where(Tool.in_code_tool_id.is_not(None))
        ).all()

        # Get tools assigned to user's teams
        user_team_ids = user.user_team_ids
        team_tools = []
        if user_team_ids:
            team_tools = db_session.scalars(
                select(Tool)
                .join(Tool__UserTeams)
                .where(Tool__UserTeams.user_group_id.in_(user_team_ids))
            ).all()

        # Combine built-in tools and team tools, removing duplicates
        all_tools = list(built_in_tools) + list(team_tools)
        seen_ids = set()
        unique_tools = []
        for tool in all_tools:
            if tool.id not in seen_ids:
                unique_tools.append(tool)
                seen_ids.add(tool.id)

        return unique_tools

    return []


def get_tool_by_id(tool_id: int, db_session: Session) -> Tool:
    tool = db_session.scalar(select(Tool).where(Tool.id == tool_id))
    if not tool:
        raise ValueError("Tool by specified id does not exist")
    return tool


def get_tool_by_name(tool_name: str, db_session: Session) -> Tool:
    tool = db_session.scalar(select(Tool).where(Tool.name == tool_name))
    if not tool:
        raise ValueError("Tool by specified name does not exist")
    return tool


def create_tool(
    name: str,
    description: str | None,
    openapi_schema: dict[str, Any] | None,
    custom_headers: list[Header] | None,
    user_id: UUID | None,
    db_session: Session,
    passthrough_auth: bool,
    is_public: bool,
    user_teams: list[int] | None = None,
) -> Tool:
    existing_tool = db_session.scalar(select(Tool).where(func.lower(Tool.name) == name.lower()))
    if existing_tool:
        raise ValueError(f"Cannot create tool with this name")
    new_tool = Tool(
        name=name,
        description=description,
        in_code_tool_id=None,
        openapi_schema=openapi_schema,
        custom_headers=[header.model_dump() for header in custom_headers]
        if custom_headers
        else [],
        user_id=user_id,
        passthrough_auth=passthrough_auth,
        is_public=is_public
    )
    db_session.add(new_tool)
    db_session.flush()  # get new_tool.id
    if user_teams:
        for group_id in user_teams:
            db_session.execute(
                Tool__UserTeams.__table__.insert().values(tool_id=new_tool.id, user_group_id=group_id)
            )
    db_session.commit()
    return new_tool


def update_tool(
    tool_id: int,
    name: str | None,
    description: str | None,
    openapi_schema: dict[str, Any] | None,
    custom_headers: list[Header] | None,
    user_id: UUID | None,
    db_session: Session,
    passthrough_auth: bool | None,
    is_public: bool | None,
    user_teams: list[int] | None = None,
) -> Tool:
    tool = get_tool_by_id(tool_id, db_session)
    if tool is None:
        raise ValueError(f"Tool with ID {tool_id} does not exist")
    if name is not None:
        existing_tool = db_session.scalar(select(Tool).where(func.lower(Tool.name) == name.lower()))
        if existing_tool and existing_tool.id != tool_id: # Allow update if the existing tool is the one being updated
            raise ValueError(f"Cannot update tool to this name")
        tool.name = name
    if description is not None:
        tool.description = description
    if openapi_schema is not None:
        tool.openapi_schema = openapi_schema
    if user_id is not None:
        tool.user_id = user_id
    if custom_headers is not None:
        tool.custom_headers = [
            cast(HeaderItemDict, header.model_dump()) for header in custom_headers
        ]
    if passthrough_auth is not None:
        tool.passthrough_auth = passthrough_auth
    if is_public is not None:
        tool.is_public = is_public
    if user_teams is not None:
        # Remove all existing associations
        db_session.execute(
            Tool__UserTeams.__table__.delete().where(Tool__UserTeams.tool_id == tool_id)
        )
        # Add new associations
        for group_id in user_teams:
            db_session.execute(
                Tool__UserTeams.__table__.insert().values(tool_id=tool_id, user_group_id=group_id)
            )
    db_session.commit()
    return tool


def delete_tool(tool_id: int, db_session: Session) -> None:
    tool = get_tool_by_id(tool_id, db_session)
    if tool is None:
        raise ValueError(f"Tool with ID {tool_id} does not exist")

    db_session.delete(tool)
    db_session.commit()


def can_user_edit_tool(user: User, tool: Tool) -> bool:
    """
    Check if a user can edit/delete a tool.
    - Admin users can edit any tool
    - Team admin users can only edit tools assigned to their teams
    """
    if user.role == UserRole.ADMIN:
        return True

    if user.role in (UserRole.TEAM_ADMIN, UserRole.BASIC):
        # Check if any of the user's teams are assigned to this tool
        user_team_ids = set(user.user_team_ids)
        tool_team_ids = set(team.id for team in tool.user_teams)
        return bool(user_team_ids.intersection(tool_team_ids))

    return False
