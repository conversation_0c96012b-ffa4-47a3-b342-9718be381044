from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTT<PERSON>Exception
from fastapi import Query
from sqlalchemy.orm import Session
from sqlalchemy import func

from onyx.auth.users import current_curator_or_admin_user
from onyx.auth.users import current_team_admin_or_admin_user
from onyx.auth.users import current_user
from onyx.auth.schemas import UserRole
from onyx.background.celery.versioned_apps.primary import app as primary_app
from onyx.configs.constants import OnyxCeleryPriority
from onyx.configs.constants import OnyxCeleryTask
from onyx.db.document_set import check_document_sets_are_public
from onyx.db.document_set import fetch_all_document_sets_for_user
from onyx.db.document_set import insert_document_set
from onyx.db.document_set import mark_document_set_as_to_be_deleted
from onyx.db.document_set import update_document_set
from onyx.db.engine import get_current_tenant_id
from onyx.db.engine import get_session
from onyx.db.models import DocumentSet as DocumentSetDBModel
from onyx.db.models import User
from onyx.server.features.document_set.models import CheckDocSetPublicRequest
from onyx.server.features.document_set.models import CheckDocSetPublicResponse
from onyx.server.features.document_set.models import DocumentSet
from onyx.server.features.document_set.models import DocumentSetCreationRequest
from onyx.server.features.document_set.models import DocumentSetUpdateRequest
from onyx.utils.user_teams_validation import validate_user_teams_for_private_resource



router = APIRouter(prefix="/manage")


@router.post("/admin/document-set")
def create_document_set(
    document_set_creation_request: DocumentSetCreationRequest,
    user: User = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
    tenant_id: str = Depends(get_current_tenant_id),
) -> int:
    try:
        existing_with_name = (
            db_session.query(DocumentSetDBModel)
            .filter(func.lower(DocumentSetDBModel.name) == document_set_creation_request.name.lower())
            .one_or_none()
        )
        if existing_with_name:
            raise ValueError("Cannot create a document set with this name.")
        
        if user.role == UserRole.ADMIN:
            # Validate user_teams for private document sets
            validate_user_teams_for_private_resource(
                is_public=document_set_creation_request.is_public,
                user_teams=document_set_creation_request.user_teams,
                resource_type="document set",
                db_session=db_session
            )

        # Auto-populate team_id for TEAM_ADMIN 
        
        if user.role==UserRole.TEAM_ADMIN:
            if document_set_creation_request.is_public:
                raise HTTPException(
                    status_code=400,
                    detail="You cannot set document set as public"
                )

            # The user must belong to at least one team
            if not user.user_team_ids:
                raise HTTPException(
                    status_code=400,
                    detail="You must be a member of at least one team to create a document set."
                )

            if document_set_creation_request.user_teams:
                raise HTTPException(
                    status_code=400,
                    detail="You are not allowed to assign a team to a document set."
                )

            # Auto-populate with user's teams
            document_set_creation_request.user_teams = user.user_team_ids
            document_set_creation_request.is_public = False
            document_set_creation_request.users = []
            document_set_creation_request.groups = []

        document_set_db_model, _ = insert_document_set(
            document_set_creation_request=document_set_creation_request,
            user_id=user.id if user else None,
            db_session=db_session,
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

    primary_app.send_task(
        OnyxCeleryTask.CHECK_FOR_VESPA_SYNC_TASK,
        kwargs={"tenant_id": tenant_id},
        priority=OnyxCeleryPriority.HIGH,
    )

    return document_set_db_model.id


@router.patch("/admin/document-set")
def patch_document_set(
    document_set_update_request: DocumentSetUpdateRequest,
    user: User = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
    tenant_id: str = Depends(get_current_tenant_id),
) -> None:
    try:
        # Validate user_teams for private document sets
        if user.role == UserRole.ADMIN:
            # Validate user_teams for private document sets
            validate_user_teams_for_private_resource(
                is_public=document_set_update_request.is_public,
                user_teams=document_set_update_request.user_teams,
                resource_type="document set",
                db_session=db_session
            )

        # Auto-populate for TEAM_ADMIN 
        if user.role==UserRole.TEAM_ADMIN:
            if document_set_update_request.is_public:
                raise HTTPException(
                    status_code=400,
                    detail="You cannot set document set as public"
                )

            # The user must belong to at least one team
            if not user.user_team_ids:
                raise HTTPException(
                    status_code=400,
                    detail="You must be a member of at least one team to create a document set."
                )

            if document_set_update_request.user_teams:
                raise HTTPException(
                    status_code=400,
                    detail="You are not allowed to assign a team to a document set."
                )

            # Auto-populate with user's teams
            document_set_update_request.user_teams = user.user_team_ids
            document_set_update_request.is_public = False
            document_set_update_request.users = []
            document_set_update_request.groups = []

        update_document_set(
            document_set_update_request=document_set_update_request,
            db_session=db_session,
            user=user,
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

    primary_app.send_task(
        OnyxCeleryTask.CHECK_FOR_VESPA_SYNC_TASK,
        kwargs={"tenant_id": tenant_id},
        priority=OnyxCeleryPriority.HIGH,
    )


@router.delete("/admin/document-set/{document_set_id}")
def delete_document_set(
    document_set_id: int,
    user: User = Depends(current_team_admin_or_admin_user),
    db_session: Session = Depends(get_session),
    tenant_id: str = Depends(get_current_tenant_id),
) -> None:
    try:
        mark_document_set_as_to_be_deleted(
            db_session=db_session,
            document_set_id=document_set_id,
            user=user,
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

    primary_app.send_task(
        OnyxCeleryTask.CHECK_FOR_VESPA_SYNC_TASK,
        kwargs={"tenant_id": tenant_id},
        priority=OnyxCeleryPriority.HIGH,
    )


"""Endpoints for non-admins"""


@router.get("/document-set")
def list_document_sets_for_user(
    user: User | None = Depends(current_user),
    db_session: Session = Depends(get_session),
    get_editable: bool = Query(
        False, description="If true, return editable document sets"
    ),
) -> list[DocumentSet]:
    return [
        DocumentSet.from_model(ds)
        for ds in fetch_all_document_sets_for_user(
            db_session=db_session, user=user, get_editable=get_editable
        )
    ]


@router.get("/document-set-public")
def document_set_public(
    check_public_request: CheckDocSetPublicRequest,
    _: User = Depends(current_user),
    db_session: Session = Depends(get_session),
) -> CheckDocSetPublicResponse:
    is_public = check_document_sets_are_public(
        document_set_ids=check_public_request.document_set_ids, db_session=db_session
    )
    return CheckDocSetPublicResponse(is_public=is_public)
