"""Utility functions for validating user_teams parameters."""

from fastapi import HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import select

from onyx.db.models import UserGroup


def validate_user_team_exists(user_team_id: int, db_session: Session) -> None:
    """
    Validates that a user_team exists and is up to date.

    Args:
        user_team_id: ID of the user team to validate
        db_session: Database session

    Raises:
        HTTPException: If user_team does not exist or is not up to date
    """
    user_team = db_session.scalar(
        select(UserGroup).where(
            UserGroup.id == user_team_id,
            UserGroup.is_up_to_date == True  # noqa: E712
        )
    )

    if not user_team:
        raise HTTPException(
            status_code=400,
            detail="user_team does not exist"
        )


def validate_user_teams_single_selection(
    user_teams: list[int] | None,
    field_name: str = "user_teams",
    db_session: Session | None = None
) -> None:
    """
    Validates that exactly one user_team is provided and exists.

    Args:
        user_teams: List of user team IDs
        field_name: Name of the field being validated (for error messages)
        db_session: Database session for existence validation (optional)

    Raises:
        HTTPException: If user_teams is empty, None, contains more than one team, or team doesn't exist
    """

    if not user_teams or len(user_teams) == 0:
        raise HTTPException(
            status_code=400,
            detail=f"Minimum one {field_name} must be assigned"
        )

    if len(user_teams) > 1:
        raise HTTPException(
            status_code=400,
            detail=f"Only one {field_name} is allowed"
        )
    
    # Validate that the user_team exists if db_session is provided
    if db_session is not None:
        validate_user_team_exists(user_teams[0], db_session)


def validate_user_teams_for_private_resource(
    is_public: bool,
    user_teams: list[int] | None,
    resource_type: str = "resource",
    db_session: Session | None = None
) -> None:
    """
    Validates user_teams for private resources.
    For private resources, exactly one user_team must be assigned and must exist.
    For public resources, user_teams should be empty.

    Args:
        is_public: Whether the resource is public
        user_teams: List of user team IDs
        resource_type: Type of resource being validated (for error messages)
        db_session: Database session for existence validation (optional)

    Raises:
        HTTPException: If validation fails
    """
    if is_public:
        if user_teams and len(user_teams) > 0:
            raise HTTPException(
                status_code=400,
                detail=f"Public {resource_type} cannot have user teams assigned"
            )
    else:
        # Private resource - must have exactly one user_team that exists
        validate_user_teams_single_selection(user_teams, "user_team", db_session)
