#!/usr/bin/env python3
"""
Simple test script to verify chat session creation with persona validation works correctly.
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8080"  # Adjust if your backend runs on a different port
API_ENDPOINT = f"{BASE_URL}/api/chat/create-chat-session"

def test_create_chat_session_with_valid_persona():
    """Test creating a chat session with a valid persona ID (0 - default)"""
    payload = {
        "persona_id": 0,
        "description": "Test chat session"
    }
    
    try:
        response = requests.post(API_ENDPOINT, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Successfully created chat session with valid persona")
            return True
        else:
            print("❌ Failed to create chat session with valid persona")
            return False
            
    except Exception as e:
        print(f"❌ Error testing valid persona: {e}")
        return False

def test_create_chat_session_with_invalid_persona():
    """Test creating a chat session with an invalid persona ID"""
    payload = {
        "persona_id": 99999,  # Assuming this persona doesn't exist
        "description": "Test chat session with invalid persona"
    }
    
    try:
        response = requests.post(API_ENDPOINT, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 400:
            print("✅ Correctly rejected invalid persona")
            return True
        else:
            print("❌ Should have rejected invalid persona")
            return False
            
    except Exception as e:
        print(f"❌ Error testing invalid persona: {e}")
        return False

def main():
    print("Testing chat session creation with persona validation...")
    print("=" * 60)
    
    # Test 1: Valid persona
    print("\n1. Testing with valid persona (ID: 0)")
    test1_passed = test_create_chat_session_with_valid_persona()
    
    # Test 2: Invalid persona
    print("\n2. Testing with invalid persona (ID: 99999)")
    test2_passed = test_create_chat_session_with_invalid_persona()
    
    print("\n" + "=" * 60)
    print("Summary:")
    print(f"Valid persona test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Invalid persona test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The persona validation is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
